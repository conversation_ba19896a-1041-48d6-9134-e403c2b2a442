import { proxy, subscribe, snapshot } from 'valtio'
import type {
  SharedStateOptions,
  StateWrapper,
  SerializableState,
  ConnectionStatus,
  StateManagerEvent,
  EventListener,
} from '../types'
import { StorageAdapter } from './StorageAdapter'
import { SyncEngine } from './SyncEngine'
import { generateVersion } from '../utils'
import { isEqual } from 'lodash'

/**
 * Main class for managing shared state across tabs
 */
export class SharedStateManager<T extends SerializableState> {
  private proxyState: T
  private storage: StorageAdapter<T>
  private syncEngine: SyncEngine<T>
  private options: Required<SharedStateOptions>
  private unsubscribe: (() => void) | null = null
  private lastSyncTime: number | null = null
  private errors: string[] = []

  constructor(initialState: T, options: SharedStateOptions = {}) {
    // Set default options
    this.options = {
      storageKey: 'valtab-state',
      persist: true,
      persistOnClose: true,
      channelName: options.channelName || options.storageKey || 'valtab-state',
      conflictResolution: 'last-write-wins',
      debug: false,
      ...options,
    }

    // Initialize storage and sync engine
    this.storage = new StorageAdapter<T>(this.options.storageKey, this.options.debug)
    this.syncEngine = new SyncEngine<T>(
      this.options.channelName,
      this.options.conflictResolution,
      this.options.debug
    )

    // Load initial state
    const loadedState = this.loadInitialState(initialState)
    this.proxyState = proxy(loadedState)

    // Setup subscriptions
    this.setupSubscriptions()
    this.setupEventListeners()

    // Setup cleanup on page unload
    if (this.options.persistOnClose) {
      this.setupUnloadHandler()
    }

    this.log('SharedStateManager initialized')
  }

  /**
   * Get the proxy state for direct mutation
   */
  getState(): T {
    return this.proxyState
  }

  /**
   * Get a snapshot of the current state
   */
  getSnapshot(): T {
    return snapshot(this.proxyState) as T
  }

  /**
   * Get the unique tab identifier
   */
  getTabId(): string {
    return this.syncEngine.getTabId()
  }

  /**
   * Check if connected to other tabs
   */
  isConnected(): boolean {
    return this.syncEngine.isConnected()
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): ConnectionStatus {
    return {
      isConnected: this.syncEngine.isConnected(),
      connectedTabs: this.syncEngine.getConnectedTabsCount(),
      lastSync: this.lastSyncTime,
      errors: [...this.errors],
    }
  }

  /**
   * Force synchronization with other tabs
   */
  forceSync(): void {
    const currentState = this.createStateWrapper()
    this.syncEngine.broadcastUpdate(currentState)
    this.log('Forced sync triggered')
  }

  /**
   * Clear all persisted data
   */
  clearStorage(): void {
    this.storage.clear()
    this.log('Storage cleared')
  }

  /**
   * Add event listener
   */
  on(event: StateManagerEvent, listener: EventListener): void {
    this.syncEngine.on(event, listener)
  }

  /**
   * Remove event listener
   */
  off(event: StateManagerEvent, listener: EventListener): void {
    this.syncEngine.off(event, listener)
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.unsubscribe) {
      this.unsubscribe()
      this.unsubscribe = null
    }

    this.syncEngine.destroy()
    this.log('SharedStateManager destroyed')
  }

  private loadInitialState(initialState: T): T {
    if (!this.options.persist || !this.storage.isAvailable()) {
      this.log('Using initial state (no persistence)')
      return initialState
    }

    const stored = this.storage.load()
    if (!stored) {
      this.log('No stored state found, using initial state')
      return initialState
    }

    this.log('Loaded state from storage:', stored.data)
    return stored.data
  }

  private setupSubscriptions(): void {
    // Subscribe to state changes and broadcast to other tabs
    this.unsubscribe = subscribe(this.proxyState, () => {
      const currentState = this.createStateWrapper()

      // Save to storage if persistence is enabled
      if (this.options.persist && this.storage.isAvailable()) {
        this.storage.save(currentState)
      }

      // Broadcast to other tabs
      this.syncEngine.broadcastUpdate(currentState)

      this.log('State changed and synced:', currentState.data)
    })
  }

  private setupEventListeners(): void {
    // Handle incoming sync messages
    this.syncEngine.on('sync', (data) => {
      this.handleRemoteStateUpdate(data.remoteState, data.fromTab)
    })

    // Handle connection events
    this.syncEngine.on('connected', () => {
      this.lastSyncTime = Date.now()
    })

    this.syncEngine.on('error', (error) => {
      this.errors.push(error.message || 'Unknown error')
      // Keep only last 10 errors
      if (this.errors.length > 10) {
        this.errors = this.errors.slice(-10)
      }
    })
  }

  private setupUnloadHandler(): void {
    const handleUnload = () => {
      if (this.options.persist && this.storage.isAvailable()) {
        const currentState = this.createStateWrapper()
        this.storage.save(currentState)
      }
    }

    window.addEventListener('beforeunload', handleUnload)
    window.addEventListener('pagehide', handleUnload)
  }

  private handleRemoteStateUpdate(
    remoteStateWrapper: StateWrapper<T>,
    fromTab: string
  ): void {
    const currentStateWrapper = this.createStateWrapper()

    // Check if we need to update (avoid unnecessary updates)
    if (isEqual(currentStateWrapper.data, remoteStateWrapper.data)) {
      this.log('Remote state is identical, skipping update')
      return
    }

    // Resolve conflicts
    const resolvedState = this.syncEngine.resolveConflict(
      currentStateWrapper,
      remoteStateWrapper
    )

    // Update local state if resolved state is different
    if (!isEqual(currentStateWrapper.data, resolvedState.data)) {
      // Temporarily unsubscribe to avoid triggering our own broadcast
      if (this.unsubscribe) {
        this.unsubscribe()
      }

      // Update the proxy state
      Object.assign(this.proxyState, resolvedState.data)

      // Re-subscribe
      this.setupSubscriptions()

      // Save to storage
      if (this.options.persist && this.storage.isAvailable()) {
        this.storage.save(resolvedState)
      }

      this.lastSyncTime = Date.now()
      this.log('Applied remote state update from tab:', fromTab)
    }
  }

  private createStateWrapper(): StateWrapper<T> {
    return {
      data: snapshot(this.proxyState) as T,
      metadata: {
        version: generateVersion(),
        lastModified: Date.now(),
        modifiedBy: this.syncEngine.getTabId(),
      },
    }
  }

  private log(...args: any[]): void {
    if (this.options.debug) {
      console.log(
        `[SharedStateManager:${this.syncEngine.getTabId().slice(-8)}]`,
        ...args
      )
    }
  }
}
