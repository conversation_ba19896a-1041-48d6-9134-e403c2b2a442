import { merge, cloneDeep, isEqual, uniqueId } from 'lodash'

/**
 * Deep merge two objects using lodash
 * Arrays are replaced, not merged (lodash default behavior)
 */
export function deepMerge<T extends Record<string, any>>(
  target: T,
  source: Partial<T>
): T {
  return merge({}, target, source)
}

/**
 * Shallow merge two objects
 */
export function shallowMerge<T extends Record<string, any>>(
  target: T,
  source: Partial<T>
): T {
  return { ...target, ...source }
}

/**
 * Generate a unique tab identifier
 * Uses crypto.randomUUID if available, falls back to lodash uniqueId
 */
export function generateTabId(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID()
  }

  // Fallback using lodash uniqueId with timestamp
  return `tab-${Date.now()}-${uniqueId()}`
}

/**
 * Generate a version number based on timestamp
 */
export function generateVersion(): number {
  return Date.now()
}
